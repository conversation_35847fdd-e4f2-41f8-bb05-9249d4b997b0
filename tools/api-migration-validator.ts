/**
 * API迁移验证工具
 * 基于@mswjs/interceptors和jsondiffpatch的API迁移验证解决方案
 */

import { BaseTool } from '../utils/tool-template';
import { Modal } from '../utils/ui-components';
// import { notificationManager } from '../utils/notification-manager'; // 暂时注释，未使用

// 核心类型定义
interface InterceptRule {
  id: string;
  name: string;
  description?: string;
  enabled: boolean;
  priority: number;
  createdAt: number;
  updatedAt: number;
  conditions: {
    urlPattern: string;
    urlMatchType: 'contains' | 'startsWith' | 'endsWith' | 'regex' | 'exact';
    methods?: string[];
    headers?: Record<string, string>;
  };
  transformation: {
    newUrl: string;
    paramMapping?: Record<string, string>;
    // 移除headerMapping，简化配置
    preserveOriginalParams?: boolean;
    includeCookies?: boolean;
  };
  // 移除mode属性，只使用并行对比模式
  diffConfig?: {
    ignoreFields?: string[];
    caseSensitive?: boolean;
    arrayOrderSensitive?: boolean;
    numericTolerance?: number;
  };
}

interface DiffReport {
  id: string;
  ruleId: string;
  ruleName: string;
  timestamp: number;
  request: {
    url: string;
    method: string;
    headers: Record<string, string>;
    body?: any;
    newUrl?: string;
  };
  responses: {
    old: { status: number; body: any; responseTime: number };
    new: { status: number; body: any; responseTime: number };
  };
  diff: {
    delta: any;
    hasChanges: boolean;
    changeCount: number;
    severity: 'none' | 'minor' | 'major' | 'critical';
  };
  visualizations: {
    html: string;
    summary?: string;
  };
}

export class ApiMigrationValidatorTool extends BaseTool {
  id = 'api-migration-validator';
  name = 'API迁移验证工具';
  description = '拦截API请求，验证新老接口的兼容性，支持重定向和并行对比两种模式';
  icon = '🔄';
  categories = ['all'];
  version = { major: 1, minor: 0, patch: 0 };

  // 核心组件
  private interceptor: any = null;
  private rules: InterceptRule[] = [];
  private reports: DiffReport[] = [];
  private isIntercepting = false;
  private messageListener: any = null;
  private processedReportIds: Set<string> = new Set();
  private pollingInterval: any = null;

  // 此工具使用外部npm包依赖，在运行时动态检查
  dependencies = [];

  async action(): Promise<void> {
    console.log('🚀 API迁移验证工具被点击，开始执行...');
    
    try {
      // 检查依赖
      console.log('📦 检查外部依赖...');
      if (!await this.checkDependencies()) {
        console.log('❌ 依赖检查失败，显示安装指南');
        await this.showDependencyInstallGuide();
        return;
      }
      console.log('✅ 依赖检查通过');

      // 创建主界面（先显示界面，后台异步加载数据）
      console.log('�️ 创建主界面模态框...');
      const modal = this.createMainModal();
      console.log('✅ 主界面模态框创建完成');

      // 添加到DOM
      console.log('� 将模态框添加到DOM...');
      document.body.appendChild(modal);
      console.log('✅ 模态框已添加到DOM');

      // 初始化UI
      console.log('🎛️ 初始化UI组件...');
      this.initializeUI(modal);
      console.log('✅ UI初始化完成');

      // 暴露到全局对象供HTML onclick使用
      (window as any).apiMigrationValidator = this;
      console.log('� 工具实例已暴露到全局对象 window.apiMigrationValidator');
      console.log('�🎉 API迁移验证工具启动完成！');

      // 强制激活Service Worker并异步加载数据
      this.activateServiceWorkerAndLoadData(modal);

    } catch (error) {
      console.error('API迁移验证工具启动失败:', error);
      await this.showNotification('错误', 'API迁移验证工具启动失败');
    }
  }

  /**
   * 强制激活Service Worker并加载数据
   */
  private async activateServiceWorkerAndLoadData(modal: HTMLElement): Promise<void> {
    console.log('🔄 强制激活Service Worker...');

    try {
      // 首先检查runtime连接状态
      console.log('🔍 检查runtime连接状态...');
      console.log('Runtime ID:', browser.runtime.id);
      console.log('Runtime lastError:', browser.runtime.lastError);

      // 检查扩展上下文是否有效
      try {
        const manifest = browser.runtime.getManifest();
        console.log('✅ 扩展上下文有效，manifest版本:', manifest.manifest_version);
      } catch (manifestError) {
        console.error('❌ 扩展上下文无效:', manifestError);
        throw new Error('扩展上下文已失效，请重新加载扩展');
      }

      console.log('🔄 开始异步加载数据...');

      // 立即开始加载本地数据（不依赖Service Worker）
      const dataLoadPromise = this.loadDataAsync(modal);

      // 同时异步唤醒Service Worker（不阻塞数据加载）
      const serviceWorkerPromise = this.wakeUpServiceWorkerAsync();

      // 等待数据加载完成（这是用户最关心的）
      await dataLoadPromise;

      // Service Worker唤醒在后台继续进行，不阻塞UI
      serviceWorkerPromise.catch((error: any) => {
        console.warn('⚠️ Service Worker唤醒失败，但不影响基本功能:', error);
      });



    } catch (error) {
      console.error('❌ Service Worker激活过程出错:', error);
      // 即使激活失败，也尝试加载本地数据
      await this.loadDataAsync(modal);
    }
  }

  /**
   * 异步唤醒Service Worker - 不阻塞数据加载
   */
  private async wakeUpServiceWorkerAsync(): Promise<boolean> {
    console.log('🚀 开始异步唤醒Service Worker...');

    const startTime = Date.now();

    // 第一步：强制唤醒序列
    await this.forceWakeUpServiceWorker();

    // 第二步：快速检测
    try {
      console.log('🏓 快速检测Service Worker状态...');
      const quickResponse = await Promise.race([
        browser.runtime.sendMessage({ type: 'ping' }),
        new Promise((_, reject) => setTimeout(() => reject(new Error('快速检测超时')), 800))
      ]);

      if (quickResponse && quickResponse.success) {
        const responseTime = Date.now() - startTime;
        console.log(`✅ Service Worker已激活 (快速响应: ${responseTime}ms)`);
        return true;
      }
    } catch (error) {
      console.log('⚠️ 快速检测失败，进入渐进式检测...');
    }

    // 第三步：渐进式检测（如果快速检测失败）
    const maxAttempts = 4; // 进一步减少尝试次数
    for (let i = 0; i < maxAttempts; i++) {
      try {
        console.log(`🏓 渐进式检测 (${i + 1}/${maxAttempts})...`);

        const timeout = 2000 + (i * 1000); // 2s, 3s, 4s, 5s
        const pingResponse = await Promise.race([
          browser.runtime.sendMessage({ type: 'ping' }),
          new Promise((_, reject) => setTimeout(() => reject(new Error('Ping超时')), timeout))
        ]);

        if (pingResponse && pingResponse.success) {
          const totalTime = Date.now() - startTime;
          console.log(`✅ Service Worker已激活 (总耗时: ${totalTime}ms)`);
          return true;
        }
      } catch (error) {
        console.log(`⚠️ 第${i + 1}次检测失败:`, error);
        if (i < maxAttempts - 1) {
          await new Promise(resolve => setTimeout(resolve, 500 + (i * 300)));
        }
      }
    }

    console.warn('⚠️ Service Worker异步唤醒失败');
    return false;
  }

  /**
   * 强制唤醒Service Worker - 增强版本
   */
  private async forceWakeUpServiceWorker(): Promise<void> {
    try {
      console.log('🚀 执行强制唤醒Service Worker序列 (增强版本)');

      // 第一轮：基础存储操作
      await Promise.all([
        browser.storage.local.set({ _forceWakeUp: Date.now() }),
        browser.storage.local.set({ _wakeUpTrigger: Date.now() + 1 }),
        browser.storage.local.set({ _heartbeatTrigger: Date.now() + 2 }),
        browser.storage.local.set({ _activationSequence: Math.random() })
      ]);

      // 第二轮：API调用组合
      await Promise.all([
        browser.runtime.getPlatformInfo(),
        browser.storage.local.get(['lastKeepAlive']),
        browser.storage.local.get(['wakeUpCounter']),
        browser.management.getSelf().catch(() => null),
        browser.tabs.query({ active: true }).catch(() => [])
      ]);

      // 第三轮：连续轻量级操作
      for (let i = 0; i < 5; i++) {
        await browser.storage.local.set({ [`_sequence_${i}`]: Date.now() + i });
        if (i < 4) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      // 第四轮：最终激活确认
      await browser.storage.local.set({
        _wakeUpComplete: Date.now(),
        _totalWakeUpAttempts: (await browser.storage.local.get(['_totalWakeUpAttempts']))._totalWakeUpAttempts + 1 || 1
      });

      console.log('✅ 强制唤醒序列执行完成');
    } catch (error) {
      console.error('❌ 强制唤醒序列执行失败:', error);
    }
  }

  /**
   * 诊断Service Worker问题
   */
  private async diagnoseServiceWorkerIssue(): Promise<void> {
    try {
      console.log('🔍 开始诊断Service Worker问题...');
      
      // 检查扩展基本信息
      const manifest = browser.runtime.getManifest();
      console.log('📋 Manifest信息:', {
        name: manifest.name,
        version: manifest.version,
        manifest_version: manifest.manifest_version
      });
      
      // 检查权限
      const permissions = manifest.permissions || [];
      console.log('🔐 权限列表:', permissions);
      
      // 检查存储状态
      const storageResult = await browser.storage.local.get(null);
      console.log('💾 存储键数量:', Object.keys(storageResult).length);
      
      // 尝试获取扩展信息
      try {
        const platformInfo = await browser.runtime.getPlatformInfo();
        console.log('💻 平台信息:', platformInfo);
      } catch (error) {
        console.error('❌ 获取平台信息失败:', error);
      }
      
      console.log('🔍 诊断完成');
    } catch (error) {
      console.error('❌ 诊断失败:', error);
    }
  }

  /**
   * 异步加载数据（不阻塞界面显示）
   */
  private async loadDataAsync(modal: HTMLElement): Promise<void> {
    console.log('🔄 开始异步加载数据...');

    // 并行加载规则、状态和报告
    const [rulesResult, statusResult, reportsResult] = await Promise.allSettled([
      this.loadRules(),
      this.loadInterceptorStatus(),
      this.loadReportsFromStorage()
    ]);

    // 处理规则加载结果
    if (rulesResult.status === 'fulfilled') {
      console.log('✅ 规则加载完成');
    } else {
      console.warn('⚠️ 规则加载失败:', rulesResult.reason);
    }

    // 处理状态加载结果
    if (statusResult.status === 'fulfilled') {
      console.log('✅ 状态加载完成');
    } else {
      console.warn('⚠️ 状态加载失败:', statusResult.reason);
    }

    // 处理报告加载结果
    if (reportsResult.status === 'fulfilled') {
      console.log('✅ 报告加载完成');
    } else {
      console.warn('⚠️ 报告加载失败:', reportsResult.reason);
    }

    // 更新UI显示
    this.updateUIAfterDataLoad(modal);
    console.log('🎯 数据加载完成，UI已更新');
  }

  /**
   * 加载拦截器状态
   */
  private async loadInterceptorStatus(): Promise<void> {
    try {
      console.log('📡 检查background script中的拦截器状态...');

      // 添加5秒超时，状态获取失败不影响界面显示
      const statusResponse = await Promise.race([
        this.sendMessageToBackground('getStatus'),
        new Promise<any>(resolve =>
          setTimeout(() => resolve({ success: false, error: '状态获取超时' }), 5000)
        )
      ]);

      console.log('📊 状态响应详情:', JSON.stringify(statusResponse, null, 2));

      if (statusResponse.success && statusResponse.data) {
        this.isIntercepting = statusResponse.data.isIntercepting;
        console.log('🔄 当前拦截器状态:', this.isIntercepting ? '运行中' : '已停止');
        console.log('📊 规则数量:', statusResponse.data.rulesCount);
      } else {
        console.log('⚠️ 获取状态失败，使用默认状态:', statusResponse.error || '未知错误');
        this.isIntercepting = false; // 默认为停止状态
      }
    } catch (error) {
      console.warn('⚠️ 获取拦截器状态异常，使用默认状态:', error);
      this.isIntercepting = false; // 默认为停止状态
    }
  }

  /**
   * 数据加载完成后更新UI
   */
  private updateUIAfterDataLoad(modal: HTMLElement): void {
    // 更新状态显示
    const statusBadge = modal.querySelector('#status-badge') as HTMLElement;
    const toggleBtn = modal.querySelector('#toggle-interceptor') as HTMLButtonElement;

    if (statusBadge) {
      statusBadge.textContent = this.isIntercepting ? '拦截中' : '已停止';
      statusBadge.className = `badge badge-${this.isIntercepting ? 'success' : 'secondary'}`;
    }

    if (toggleBtn) {
      toggleBtn.textContent = this.isIntercepting ? '停止拦截' : '开始拦截';
      toggleBtn.disabled = false; // 启用按钮
    }

    // 重新渲染规则和报告列表
    this.renderRulesList(modal);
    this.renderReportsList(modal);
  }

  /**
   * 检查外部依赖
   */
  private async checkDependencies(): Promise<boolean> {
    try {
      console.log('🔍 检查 @mswjs/interceptors...');
      await import('@mswjs/interceptors');
      console.log('✅ @mswjs/interceptors 可用');
      
      console.log('🔍 检查 jsondiffpatch...');
      await import('jsondiffpatch');
      console.log('✅ jsondiffpatch 可用');
      
      return true;
    } catch (error) {
      console.warn('❌ 缺少必要依赖:', error);
      return false;
    }
  }

  /**
   * 显示依赖安装指南
   */
  private async showDependencyInstallGuide(): Promise<void> {
    const modal = new Modal(`
      <div class="dependency-guide">
        <h4>📦 安装必要依赖</h4>
        <p>此工具需要以下npm包：</p>
        <div class="code-block">
          <code>npm install @mswjs/interceptors jsondiffpatch</code>
        </div>
        <p>安装完成后请重新启动工具。</p>
      </div>
    `, {
      title: '缺少依赖',
      size: 'md',
      closable: true
    });

    modal.addFooter(`
      <button class="btn btn-primary" onclick="this.closest('.modal-overlay').remove()">了解</button>
    `);

    modal.open();
  }

  /**
   * 创建主界面模态框
   */
  private createMainModal(): HTMLElement {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay api-migration-validator-modal';
    modal.innerHTML = `
      <div class="modal-content modal-xl">
        <div class="modal-header">
          <h3 class="modal-title">🔄 API迁移验证工具</h3>
          <div class="header-actions">
            <span class="badge badge-secondary" id="status-badge">
              加载中...
            </span>
            <button class="btn btn-sm btn-outline" id="toggle-interceptor" disabled>
              加载中...
            </button>
            <button class="modal-close">&times;</button>
          </div>
        </div>
        <div class="modal-body">
          <div class="tabs">
            <ul class="tabs-list">
              <li class="tabs-item">
                <a href="#rules" class="tabs-link active">拦截规则</a>
              </li>
              <li class="tabs-item">
                <a href="#reports" class="tabs-link">对比报告</a>
              </li>
              <li class="tabs-item">
                <a href="#settings" class="tabs-link">设置</a>
              </li>
            </ul>
          </div>
          
          <div class="tabs-content">
            <!-- 拦截规则标签页 -->
            <div id="rules" class="tab-pane active">
              <div class="rules-toolbar">
                <button class="btn btn-primary" id="add-rule-btn">
                  <span>➕</span> 添加规则
                </button>
                <button class="btn btn-outline" id="import-rules-btn">导入规则</button>
                <button class="btn btn-outline" id="export-rules-btn">导出规则</button>
              </div>
              <div class="rules-list" id="rules-list">
                <!-- 规则列表将在这里动态生成 -->
              </div>
            </div>
            
            <!-- 对比报告标签页 -->
            <div id="reports" class="tab-pane">
              <div class="reports-toolbar">
                <button class="btn btn-outline" id="clear-reports-btn">清空报告</button>
                <span class="reports-count">共 <span id="reports-count">0</span> 条报告</span>
              </div>
              <div class="reports-list" id="reports-list">
                <!-- 报告列表将在这里动态生成 -->
              </div>
            </div>
            
            <!-- 设置标签页 -->
            <div id="settings" class="tab-pane">
              <div class="settings-form">
                <div class="form-group">
                  <label class="form-label">最大报告数量</label>
                  <input type="number" class="form-control" id="max-reports" value="100" min="10" max="1000">
                </div>
                <div class="form-group">
                  <label class="form-label">全局忽略模式</label>
                  <textarea class="form-control" id="global-ignore" rows="3" 
                    placeholder="每行一个URL模式，支持正则表达式"></textarea>
                </div>
                <div class="form-check">
                  <input type="checkbox" class="form-check-input" id="auto-start" checked>
                  <label class="form-check-label" for="auto-start">启动时自动开始拦截</label>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" id="close-btn">关闭</button>
        </div>
      </div>
    `;

    return modal;
  }

  /**
   * 初始化UI组件和事件
   */
  private initializeUI(modal: HTMLElement): void {
    // 标签页切换
    this.initializeTabs(modal);

    // 拦截器控制
    this.initializeInterceptorControls(modal);

    // 规则管理
    this.initializeRuleManagement(modal);

    // 报告管理
    this.initializeReportManagement(modal);

    // 设置管理
    this.initializeSettings(modal);

    // 关闭事件
    this.initializeCloseEvents(modal);

    // 初始化消息监听器
    this.initializeMessageListener();

    // 渲染初始数据
    this.renderRulesList(modal);
    this.renderReportsList(modal);
  }

  /**
   * 初始化标签页切换
   */
  private initializeTabs(modal: HTMLElement): void {
    const tabLinks = modal.querySelectorAll('.tabs-link');
    const tabPanes = modal.querySelectorAll('.tab-pane');

    tabLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const target = (e.target as HTMLElement).getAttribute('href')?.substring(1);

        // 移除所有active类
        tabLinks.forEach(l => l.classList.remove('active'));
        tabPanes.forEach(p => p.classList.remove('active'));

        // 添加active类到当前标签
        (e.target as HTMLElement).classList.add('active');
        const targetPane = modal.querySelector(`#${target}`);
        if (targetPane) {
          targetPane.classList.add('active');
        }
      });
    });


  }

  /**
   * 初始化拦截器控制
   */
  private initializeInterceptorControls(modal: HTMLElement): void {
    const toggleBtn = modal.querySelector('#toggle-interceptor') as HTMLButtonElement;
    const statusBadge = modal.querySelector('#status-badge') as HTMLElement;

    toggleBtn?.addEventListener('click', async () => {
      if (this.isIntercepting) {
        await this.stopInterceptor();
        toggleBtn.textContent = '开始拦截';
        statusBadge.textContent = '已停止';
        statusBadge.className = 'badge badge-secondary';
      } else {
        await this.startInterceptor();
        toggleBtn.textContent = '停止拦截';
        statusBadge.textContent = '拦截中';
        statusBadge.className = 'badge badge-success';
      }
    });
  }

  /**
   * 加载保存的规则（带超时和降级机制）
   */
  private async loadRules(): Promise<void> {
    try {
      console.log('📡 从background script获取规则...');

      // 添加3秒超时的规则获取，失败时自动降级到本地存储
      const response = await Promise.race([
        this.sendMessageToBackground('getRules'),
        new Promise<any>(resolve =>
          setTimeout(() => resolve({ success: false, error: '获取规则超时' }), 3000)
        )
      ]);

      if (response.success && response.data) {
        this.rules = response.data;
        console.log(`✅ 从background加载了 ${this.rules.length} 条规则`);
      } else {
        console.log('⚠️ background获取规则失败，尝试从本地存储加载...');
        // 降级到本地存储
        const savedRules = await this.loadData('rules', []);
        this.rules = savedRules;
        console.log(`✅ 从本地存储加载了 ${this.rules.length} 条规则`);
      }
    } catch (error) {
      console.warn('⚠️ 规则加载异常，使用空规则列表:', error);
      this.rules = [];
      console.log('📝 使用空规则列表，不影响界面显示');
    }
  }

  /**
   * 保存规则到存储
   */
  private async saveRules(): Promise<void> {
    try {
      // 保存到background script
      await this.sendMessageToBackground('updateRules', { rules: this.rules });
      
      // 同时保存到本地存储作为备份
      await this.saveData('rules', this.rules);
    } catch (error) {
      console.error('保存规则失败:', error);
    }
  }

  /**
   * 渲染规则列表
   */
  private renderRulesList(modal: HTMLElement): void {
    const rulesList = modal.querySelector('#rules-list') as HTMLElement;
    if (!rulesList) return;

    if (this.rules.length === 0) {
      rulesList.innerHTML = `
        <div class="empty-state">
          <p>暂无拦截规则</p>
          <p class="text-muted">点击"添加规则"创建第一个API拦截规则</p>
        </div>
      `;
      return;
    }

    rulesList.innerHTML = this.rules.map(rule => `
      <div class="card rule-card" data-rule-id="${rule.id}">
        <div class="card-header">
          <div class="rule-info">
            <h4 class="card-title">${rule.name}</h4>
            <p class="rule-description">${rule.description || '无描述'}</p>
          </div>
          <div class="rule-actions">
            <span class="badge badge-${rule.enabled ? 'success' : 'secondary'}">
              ${rule.enabled ? '启用' : '禁用'}
            </span>
            <span class="badge badge-info">并行对比</span>
            <button class="btn btn-sm btn-ghost" data-action="edit">编辑</button>
            <button class="btn btn-sm btn-ghost" data-action="toggle">
              ${rule.enabled ? '禁用' : '启用'}
            </button>
            <button class="btn btn-sm btn-ghost text-error" data-action="delete">删除</button>
          </div>
        </div>
        <div class="card-body">
          <div class="rule-details">
            <div class="detail-item">
              <span class="detail-label">URL模式:</span>
              <code>${rule.conditions.urlPattern}</code>
            </div>
            <div class="detail-item">
              <span class="detail-label">匹配方式:</span>
              <span>${this.getMatchTypeLabel(rule.conditions.urlMatchType)}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">目标URL:</span>
              <code>${rule.transformation.newUrl}</code>
            </div>
            ${rule.conditions.methods ? `
              <div class="detail-item">
                <span class="detail-label">HTTP方法:</span>
                <span>${rule.conditions.methods.join(', ')}</span>
              </div>
            ` : ''}
          </div>
        </div>
      </div>
    `).join('');
  }

  /**
   * 获取匹配类型标签
   */
  private getMatchTypeLabel(type: string): string {
    const labels: Record<string, string> = {
      'exact': '精确匹配',
      'contains': '包含',
      'startsWith': '开头匹配',
      'endsWith': '结尾匹配',
      'regex': '正则表达式'
    };
    return labels[type] || type;
  }

  /**
   * 初始化规则管理
   */
  private initializeRuleManagement(modal: HTMLElement): void {
    const addRuleBtn = modal.querySelector('#add-rule-btn') as HTMLButtonElement;
    const importBtn = modal.querySelector('#import-rules-btn') as HTMLButtonElement;
    const exportBtn = modal.querySelector('#export-rules-btn') as HTMLButtonElement;
    const rulesList = modal.querySelector('#rules-list') as HTMLElement;

    // 添加规则
    addRuleBtn?.addEventListener('click', () => {
      this.showRuleEditor();
    });

    // 导入规则
    importBtn?.addEventListener('click', () => {
      this.importRules();
    });

    // 导出规则
    exportBtn?.addEventListener('click', () => {
      this.exportRules();
    });

    // 规则操作事件委托
    rulesList?.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;
      const action = target.getAttribute('data-action');
      const ruleCard = target.closest('.rule-card') as HTMLElement;
      const ruleId = ruleCard?.getAttribute('data-rule-id');

      if (!ruleId || !action) return;

      switch (action) {
        case 'edit':
          this.editRule(ruleId);
          break;
        case 'toggle':
          this.toggleRule(ruleId);
          break;
        case 'delete':
          this.deleteRule(ruleId);
          break;
      }
    });
  }

  /**
   * 初始化报告管理
   */
  private initializeReportManagement(modal: HTMLElement): void {
    const clearBtn = modal.querySelector('#clear-reports-btn') as HTMLButtonElement;
    const reportsList = modal.querySelector('#reports-list') as HTMLElement;

    // 清空报告
    clearBtn?.addEventListener('click', async () => {
      if (confirm('确定要清空所有对比报告吗？')) {
        try {
          console.log('🗑️ 开始清除所有报告数据...');
          
          // 清空内存中的报告
          this.reports = [];
          
          // 直接删除storage中的数据（而不是设置为空数组）
          const toolId = this.id;
          const storageKey1 = `tool-${toolId}-apiMigrationReports`;
          const storageKey2 = `tool-${toolId}-reports`;
          
          console.log('🔑 删除storage keys:', { storageKey1, storageKey2 });
          
          // 方法1：使用removeData方法
          try {
            await this.removeData('apiMigrationReports');
            console.log('✅ 使用removeData删除apiMigrationReports成功');
          } catch (error) {
            console.warn('⚠️ 使用removeData删除apiMigrationReports失败:', error);
          }
          
          try {
            await this.removeData('reports');
            console.log('✅ 使用removeData删除reports成功');
          } catch (error) {
            console.warn('⚠️ 使用removeData删除reports失败:', error);
          }
          
          // 方法2：直接使用browser API删除（备用方案）
          try {
            await browser.storage.local.remove([storageKey1, storageKey2]);
            console.log('✅ 使用browser API直接删除storage keys成功');
          } catch (error) {
            console.warn('⚠️ 使用browser API删除storage keys失败:', error);
          }
          
          // 方法3：设置为空数组（最后备用方案）
          try {
            await this.saveData('apiMigrationReports', []);
            await this.saveData('reports', []);
            console.log('✅ 设置空数组备用方案完成');
          } catch (error) {
            console.warn('⚠️ 设置空数组备用方案失败:', error);
          }
          
          // 验证storage是否真正被清除
          console.log('🔍 开始验证清除结果...');
          const storedReports = await this.loadData('apiMigrationReports', []);
          const storedReportsAlt = await this.loadData('reports', []);
          
          // 直接检查storage中的原始数据
          const directCheck1 = await browser.storage.local.get(storageKey1);
          const directCheck2 = await browser.storage.local.get(storageKey2);
          
          console.log('📊 清除验证结果:', {
            loadData_apiMigrationReports: storedReports.length,
            loadData_reports: storedReportsAlt.length,
            directCheck_apiMigrationReports: directCheck1[storageKey1],
            directCheck_reports: directCheck2[storageKey2]
          });
          
          const isCleared = storedReports.length === 0 && 
                           storedReportsAlt.length === 0 && 
                           !directCheck1[storageKey1] && 
                           !directCheck2[storageKey2];
          
          if (isCleared) {
            console.log('✅ 报告数据清除成功');
            // 确认清除成功后更新UI
            this.renderReportsList(modal);
            this.updateReportsCount(modal);
            await this.showNotification('成功', '已清空所有报告');
          } else {
            console.error('❌ 清除报告失败：storage中仍有数据残留', {
              loadData_apiMigrationReports: storedReports.length,
              loadData_reports: storedReportsAlt.length,
              direct_apiMigrationReports: !!directCheck1[storageKey1],
              direct_reports: !!directCheck2[storageKey2]
            });
            await this.showNotification('错误', '清除报告失败，请重试');
          }
        } catch (error) {
          console.error('❌ 清除报告时发生错误:', error);
          await this.showNotification('错误', '清除报告失败：' + (error as Error).message);
        }
      }
    });

    // 报告操作事件委托
    reportsList?.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;
      const action = target.getAttribute('data-action');
      // 更新为新的简化报告项选择器
      const reportItem = target.closest('.report-item-simple') as HTMLElement;
      const reportId = reportItem?.getAttribute('data-report-id');

      if (!reportId || !action) return;

      switch (action) {
        case 'view':
          this.viewReport(reportId);
          break;
        case 'delete':
          this.deleteReport(reportId);
          break;
      }
    });
  }

  /**
   * 初始化设置管理
   */
  private initializeSettings(modal: HTMLElement): void {
    const maxReportsInput = modal.querySelector('#max-reports') as HTMLInputElement;
    const globalIgnoreTextarea = modal.querySelector('#global-ignore') as HTMLTextAreaElement;
    const autoStartCheckbox = modal.querySelector('#auto-start') as HTMLInputElement;

    // 加载设置
    this.loadSettings().then(settings => {
      if (maxReportsInput) maxReportsInput.value = settings.maxReports.toString();
      if (globalIgnoreTextarea) globalIgnoreTextarea.value = settings.globalIgnorePatterns.join('\n');
      if (autoStartCheckbox) autoStartCheckbox.checked = settings.autoStart;
    });

    // 保存设置
    const saveSettings = async () => {
      const settings = {
        maxReports: parseInt(maxReportsInput?.value || '100'),
        globalIgnorePatterns: globalIgnoreTextarea?.value.split('\n').filter(line => line.trim()) || [],
        autoStart: autoStartCheckbox?.checked || false
      };
      await this.saveData('settings', settings);
    };

    maxReportsInput?.addEventListener('change', saveSettings);
    globalIgnoreTextarea?.addEventListener('blur', saveSettings);
    autoStartCheckbox?.addEventListener('change', saveSettings);
  }

  /**
   * 初始化消息监听器（改为轻量级轮询检查）
   */
  private initializeMessageListener(): void {
    console.log('✅ 启动轻量级报告检查机制');

    // 启动轻量级轮询，每5秒检查一次是否有新报告
    this.pollingInterval = setInterval(async () => {
      try {
        const currentReportsCount = this.reports.length;
        const storedReports = await this.loadData('apiMigrationReports', []);

        // 如果storage中的报告数量比内存中的多，说明有新报告
        if (storedReports.length > currentReportsCount) {
          console.log(`🔄 检测到新报告，更新UI (${currentReportsCount} -> ${storedReports.length})`);
          this.reports = storedReports;

          // 更新UI
          const mainModal = document.querySelector('.api-migration-validator-modal') as HTMLElement;
          if (mainModal) {
            this.renderReportsList(mainModal);
            this.updateReportsCount(mainModal);
          }
        }
      } catch (error) {
        console.warn('⚠️ 检查新报告时出错:', error);
      }
    }, 5000); // 每5秒检查一次
  }

  /**
   * 从storage加载对比报告
   */
  private async loadReportsFromStorage(): Promise<void> {
    try {
      console.log('📊 从storage加载对比报告...');

      // 使用正确的storage key格式：tool-{toolId}-{key}
      const toolId = this.id;
      const correctStorageKey = `tool-${toolId}-apiMigrationReports`;
      const oldStorageKey = `tool-${toolId}-reports`;
      
      console.log('🔑 检查storage keys:', { correctStorageKey, oldStorageKey });
      
      // 只从正确的storage key加载数据
      const loadedReports = await this.loadData('apiMigrationReports', []);
      
      // 检查是否有旧的storage key数据，如果有则清理
      const oldReports = await this.loadData('reports', []);
      if (oldReports.length > 0) {
        console.log(`🧹 发现旧的storage key中有数据，自动清理: ${oldReports.length}个报告`);
        try {
          await this.saveData('reports', []);
          console.log('✅ 旧storage key清理成功');
        } catch (error) {
          console.warn('⚠️ 清理旧storage key失败:', error);
        }
      }

      console.log(`📋 从storage加载了 ${loadedReports.length} 个报告`);

      // 更新本地报告列表
      this.reports = loadedReports;

    } catch (error) {
      console.error('❌ 从storage加载报告失败:', error);
      this.reports = []; // 使用空数组作为默认值
    }
  }

  /**
   * 处理新报告
   */
  private handleNewReport(report: any): void {
    console.log('🔍 处理新报告，原始数据:', report);

    // 确保报告有必要的属性
    if (!report.diff) {
      report.diff = { changeCount: 0, summary: '网络拦截记录' };
    }
    if (!report.severity) {
      report.severity = 'info';
    }

    console.log('✅ 报告数据已标准化:', report);

    // 添加新报告到列表开头
    this.reports.unshift(report);

    // 限制报告数量
    const maxReports = 100;
    if (this.reports.length > maxReports) {
      this.reports.length = maxReports;
    }

    // 保存报告
    this.saveData('apiMigrationReports', this.reports);

    // 更新UI
    const mainModal = document.querySelector('.api-migration-validator-modal') as HTMLElement;
    if (mainModal) {
      this.renderReportsList(mainModal);
      this.updateReportsCount(mainModal);
    }

    // 通知用户
    const changeCount = report.diff?.changeCount || 0;
    this.showNotification('对比完成', `规则 "${report.ruleName}" 执行完成，发现 ${changeCount} 处差异`);
  }

  /**
   * 初始化关闭事件
   */
  private initializeCloseEvents(modal: HTMLElement): void {
    const closeBtn = modal.querySelector('#close-btn') as HTMLButtonElement;
    const modalClose = modal.querySelector('.modal-close') as HTMLButtonElement;

    const closeModal = () => {
      // 清理轮询间隔
      if (this.pollingInterval) {
        clearInterval(this.pollingInterval);
        this.pollingInterval = null;
        console.log('🧹 已清理报告检查轮询');
      }

      modal.remove();
    };

    closeBtn?.addEventListener('click', closeModal);
    modalClose?.addEventListener('click', closeModal);

    // 点击遮罩关闭
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        closeModal();
      }
    });
  }

  /**
   * 从URL中提取API名称
   */
  private extractApiName(url: string): string {
    try {
      const urlObj = new URL(url);
      const pathname = urlObj.pathname;
      // 提取路径的最后一部分作为API名称
      const segments = pathname.split('/').filter(segment => segment.length > 0);
      return segments.length > 0 ? segments[segments.length - 1] : 'unknown-api';
    } catch (error) {
      // 如果URL解析失败，尝试从字符串中提取
      const segments = url.split('/').filter(segment => segment.length > 0);
      return segments.length > 0 ? segments[segments.length - 1] : 'unknown-api';
    }
  }

  /**
   * 渲染报告列表
   */
  private async renderReportsList(modal: HTMLElement): Promise<void> {
    const reportsList = modal.querySelector('#reports-list') as HTMLElement;
    if (!reportsList) return;

    // 从存储加载报告
    if (this.reports.length === 0) {
      try {
        const result = await this.loadData('apiMigrationReports', []);
        this.reports = result;
      } catch (error) {
        console.error('加载报告失败:', error);
        this.reports = [];
      }
    }

    if (this.reports.length === 0) {
      reportsList.innerHTML = `
        <div class="empty-state">
          <p>暂无对比报告</p>
          <p class="text-muted">启动拦截后，匹配的API请求将生成对比报告</p>
        </div>
      `;
      return;
    }

    // 简化的报告列表显示
    reportsList.innerHTML = this.reports.map(report => `
      <div class="report-item-simple" data-report-id="${report.id}">
        <div class="api-name">${this.extractApiName(report.request.url)}</div>
        <div class="report-meta">
          <span class="change-count">${(report.diff && report.diff.changeCount !== undefined) ? report.diff.changeCount : 0} 处变更</span>
          <span class="report-time">${new Date(report.timestamp).toLocaleString()}</span>
          <button class="btn btn-sm btn-primary" data-action="view">查看</button>
          <button class="btn btn-sm btn-ghost text-error" data-action="delete">删除</button>
        </div>
      </div>
    `).join('');

    this.updateReportsCount(modal);
  }

  /**
   * 更新报告数量显示
   */
  private updateReportsCount(modal: HTMLElement): void {
    const countElement = modal.querySelector('#reports-count') as HTMLElement;
    if (countElement) {
      countElement.textContent = this.reports.length.toString();
    }
  }

  /**
   * 获取严重程度颜色
   */
  private getSeverityColor(severity: string): string {
    const colors: Record<string, string> = {
      'none': 'success',
      'minor': 'info',
      'major': 'warning',
      'critical': 'error'
    };
    return colors[severity] || 'secondary';
  }

  /**
   * 加载设置
   */
  private async loadSettings(): Promise<any> {
    return await this.loadData('settings', {
      maxReports: 100,
      globalIgnorePatterns: [],
      autoStart: false
    });
  }

  /**
   * 启动拦截器
   */
  private async startInterceptor(): Promise<void> {
    try {
      if (this.isIntercepting) return;

      // 与background script通信启动拦截器
      const response = await this.sendMessageToBackground('startIntercepting');
      
      if (response.success) {
        this.isIntercepting = true;
        await this.showNotification('成功', 'API拦截器已启动');
        console.log('🔄 API拦截器已启动');
      } else {
        throw new Error(response.error || '启动失败');
      }

    } catch (error) {
      console.error('启动拦截器失败:', error);
      await this.showNotification('错误', '启动拦截器失败：' + (error as Error).message);
    }
  }

  /**
   * 停止拦截器
   */
  private async stopInterceptor(): Promise<void> {
    try {
      if (!this.isIntercepting) return;

      // 与background script通信停止拦截器
      const response = await this.sendMessageToBackground('stopIntercepting');
      
      if (response.success) {
        this.isIntercepting = false;
        await this.showNotification('成功', 'API拦截器已停止');
        console.log('⏹️ API拦截器已停止');
      } else {
        throw new Error(response.error || '停止失败');
      }

    } catch (error) {
      console.error('停止拦截器失败:', error);
      await this.showNotification('错误', '停止拦截器失败：' + (error as Error).message);
    }
  }

  /**
   * 显示规则编辑器
   */
  private showRuleEditor(rule?: InterceptRule): void {
    const modal = this.createRuleEditorModal(rule);
    document.body.appendChild(modal);

    // 初始化编辑器
    this.initializeRuleEditor(modal, rule);
  }

  /**
   * 创建规则编辑器模态框
   */
  private createRuleEditorModal(rule?: InterceptRule): HTMLElement {
    const isEdit = !!rule;
    const modal = document.createElement('div');
    modal.className = 'modal-overlay rule-editor-modal';
    modal.innerHTML = `
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title">${isEdit ? '编辑' : '创建'}API拦截规则</h3>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          <div class="rule-editor-container">
            <div class="tabs">
              <ul class="tabs-list">
                <li class="tabs-item">
                  <a href="#basic-info" class="tabs-link active">基础信息</a>
                </li>
                <li class="tabs-item">
                  <a href="#match-conditions" class="tabs-link">匹配条件</a>
                </li>
                <li class="tabs-item">
                  <a href="#transformation" class="tabs-link">转换规则</a>
                </li>
                <li class="tabs-item">
                  <a href="#diff-settings" class="tabs-link">对比设置</a>
                </li>
              </ul>
            </div>

            <div class="tabs-content">
              <!-- 基础信息 -->
              <div id="basic-info" class="tab-pane active">
                ${this.createBasicInfoForm(rule)}
              </div>

              <!-- 匹配条件 -->
              <div id="match-conditions" class="tab-pane">
                ${this.createMatchConditionsForm(rule)}
              </div>

              <!-- 转换规则 -->
              <div id="transformation" class="tab-pane">
                ${this.createTransformationForm(rule)}
              </div>

              <!-- 对比设置 -->
              <div id="diff-settings" class="tab-pane">
                ${this.createDiffSettingsForm(rule)}
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" id="cancel-rule-btn">取消</button>
          <button class="btn btn-outline" id="test-rule-btn">测试规则</button>
          <button class="btn btn-primary" id="save-rule-btn">${isEdit ? '更新' : '创建'}规则</button>
        </div>
      </div>
    `;

    return modal;
  }

  /**
   * 创建基础信息表单
   */
  private createBasicInfoForm(rule?: InterceptRule): string {
    return `
      <div class="form-section">
        <div class="form-group">
          <label class="form-label">规则名称 *</label>
          <input type="text" class="form-control" id="rule-name"
            placeholder="输入规则名称" value="${rule?.name || ''}" required>
        </div>

        <div class="form-group">
          <label class="form-label">规则描述</label>
          <textarea class="form-control form-textarea" id="rule-description"
            placeholder="描述此规则的用途和场景">${rule?.description || ''}</textarea>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label class="form-label">优先级</label>
            <input type="number" class="form-control" id="rule-priority"
              value="${rule?.priority || 100}" min="0" max="999">
            <small class="form-text text-muted">数字越大优先级越高</small>
          </div>

          <div class="form-group">
            <label class="form-label">执行模式</label>
            <div class="form-control-static">
              <span class="badge badge-info">并行对比模式</span>
              <small class="text-muted">同时调用新老接口进行对比分析</small>
            </div>
          </div>
        </div>

        <div class="form-check">
          <input type="checkbox" class="form-check-input" id="rule-enabled"
            ${rule?.enabled !== false ? 'checked' : ''}>
          <label class="form-check-label" for="rule-enabled">启用此规则</label>
        </div>
      </div>
    `;
  }

  /**
   * 创建匹配条件表单
   */
  private createMatchConditionsForm(rule?: InterceptRule): string {
    const conditions = rule?.conditions;
    return `
      <div class="form-section">
        <div class="form-group">
          <label class="form-label">URL匹配模式 *</label>
          <input type="text" class="form-control" id="url-pattern"
            placeholder="例如: /api/v1/users 或 https://api.example.com"
            value="${conditions?.urlPattern || ''}" required>
        </div>

        <div class="form-group">
          <label class="form-label">匹配方式</label>
          <select class="form-control form-select" id="url-match-type">
            <option value="contains" ${conditions?.urlMatchType === 'contains' ? 'selected' : ''}>
              包含 - URL包含指定字符串
            </option>
            <option value="startsWith" ${conditions?.urlMatchType === 'startsWith' ? 'selected' : ''}>
              开头匹配 - URL以指定字符串开头
            </option>
            <option value="endsWith" ${conditions?.urlMatchType === 'endsWith' ? 'selected' : ''}>
              结尾匹配 - URL以指定字符串结尾
            </option>
            <option value="exact" ${conditions?.urlMatchType === 'exact' ? 'selected' : ''}>
              精确匹配 - URL完全相等
            </option>
            <option value="regex" ${conditions?.urlMatchType === 'regex' ? 'selected' : ''}>
              正则表达式 - 使用正则表达式匹配
            </option>
          </select>
        </div>

        <div class="form-group">
          <label class="form-label">HTTP方法过滤</label>
          <div class="form-check-group">
            ${['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS'].map(method => `
              <div class="form-check">
                <input type="checkbox" class="form-check-input" id="method-${method}"
                  value="${method}" ${conditions?.methods?.includes(method) ? 'checked' : ''}>
                <label class="form-check-label" for="method-${method}">${method}</label>
              </div>
            `).join('')}
          </div>
          <small class="form-text text-muted">不选择则匹配所有HTTP方法</small>
        </div>

        <div class="advanced-config" id="advanced-conditions">
          <div class="advanced-config-header">
            <span>高级匹配条件</span>
            <button type="button" class="advanced-config-toggle">▼</button>
          </div>
          <div class="advanced-config-content">
            <div class="form-group">
              <label class="form-label">请求头条件</label>
              <textarea class="form-control form-textarea" id="header-conditions"
                placeholder="JSON格式，例如: {&quot;Content-Type&quot;: &quot;application/json&quot;}">${
                  conditions?.headers ? JSON.stringify(conditions.headers, null, 2) : ''
                }</textarea>
              <small class="form-text text-muted">支持字符串精确匹配和正则表达式</small>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * 创建转换规则表单
   */
  private createTransformationForm(rule?: InterceptRule): string {
    const transformation = rule?.transformation;
    return `
      <div class="form-section">
        <div class="form-group">
          <label class="form-label">新接口URL *</label>
          <input type="text" class="form-control" id="new-url"
            placeholder="例如: https://api-v2.example.com/users"
            value="${transformation?.newUrl || ''}" required>
          <small class="form-text text-muted">支持变量替换，如 {id}, {userId} 等</small>
        </div>

        <div class="form-group">
          <label class="form-label">参数映射</label>
          <textarea class="form-control form-textarea" id="param-mapping"
            placeholder="JSON格式，例如: {&quot;old_param&quot;: &quot;new_param&quot;}">${
              transformation?.paramMapping ? JSON.stringify(transformation.paramMapping, null, 2) : ''
            }</textarea>
          <small class="form-text text-muted">将旧参数名映射到新参数名</small>
        </div>

        <!-- 移除请求头映射配置，简化配置 -->

        <div class="form-check">
          <input type="checkbox" class="form-check-input" id="preserve-params"
            ${transformation?.preserveOriginalParams ? 'checked' : ''}>
          <label class="form-check-label" for="preserve-params">保留原始参数</label>
          <small class="form-text text-muted">在映射的基础上保留未映射的原始参数</small>
        </div>

        <div class="form-check">
          <input type="checkbox" class="form-check-input" id="include-cookies"
            ${transformation?.includeCookies !== false ? 'checked' : ''}>
          <label class="form-check-label" for="include-cookies">包含Cookie</label>
          <small class="form-text text-muted">请求中包含Cookie，支持跨域认证</small>
        </div>
      </div>
    `;
  }

  /**
   * 创建对比设置表单
   */
  private createDiffSettingsForm(rule?: InterceptRule): string {
    const diffConfig = rule?.diffConfig;
    return `
      <div class="form-section">
        <div class="form-group">
          <label class="form-label">忽略字段</label>
          <textarea class="form-control form-textarea" id="ignore-fields"
            placeholder="每行一个字段路径，例如:&#10;timestamp&#10;data.id&#10;meta.version">${
              diffConfig?.ignoreFields?.join('\n') || ''
            }</textarea>
          <small class="form-text text-muted">支持点号路径，如 data.user.id</small>
        </div>

        <div class="form-row">
          <div class="form-check">
            <input type="checkbox" class="form-check-input" id="case-sensitive"
              ${diffConfig?.caseSensitive !== false ? 'checked' : ''}>
            <label class="form-check-label" for="case-sensitive">大小写敏感</label>
          </div>

          <div class="form-check">
            <input type="checkbox" class="form-check-input" id="array-order-sensitive"
              ${diffConfig?.arrayOrderSensitive !== false ? 'checked' : ''}>
            <label class="form-check-label" for="array-order-sensitive">数组顺序敏感</label>
          </div>
        </div>

        <div class="form-group">
          <label class="form-label">数值容差</label>
          <input type="number" class="form-control" id="numeric-tolerance"
            value="${diffConfig?.numericTolerance || 0}" min="0" step="0.001">
          <small class="form-text text-muted">数值比较的容差范围，0表示精确匹配</small>
        </div>
      </div>
    `;
  }

  /**
   * 初始化规则编辑器
   */
  private initializeRuleEditor(modal: HTMLElement, rule?: InterceptRule): void {
    const isEdit = !!rule;

    // 初始化标签页切换
    this.initializeTabs(modal);

    // 初始化高级配置折叠
    this.initializeAdvancedConfig(modal);

    // 初始化表单验证
    this.initializeFormValidation(modal);

    // 绑定按钮事件
    const cancelBtn = modal.querySelector('#cancel-rule-btn') as HTMLButtonElement;
    const testBtn = modal.querySelector('#test-rule-btn') as HTMLButtonElement;
    const saveBtn = modal.querySelector('#save-rule-btn') as HTMLButtonElement;
    const closeBtn = modal.querySelector('.modal-close') as HTMLButtonElement;

    // 取消/关闭
    const closeModal = () => modal.remove();
    cancelBtn?.addEventListener('click', closeModal);
    closeBtn?.addEventListener('click', closeModal);

    // 测试规则
    testBtn?.addEventListener('click', () => {
      this.testRule(modal);
    });

    // 保存规则
    saveBtn?.addEventListener('click', async () => {
      const ruleData = this.extractRuleData(modal);
      if (ruleData) {
        if (isEdit) {
          await this.updateRule(rule!.id, ruleData);
        } else {
          await this.createRule(ruleData);
        }
        closeModal();
      }
    });

    // 点击遮罩关闭
    modal.addEventListener('click', (e) => {
      if (e.target === modal) closeModal();
    });
  }

  /**
   * 初始化高级配置折叠
   */
  private initializeAdvancedConfig(modal: HTMLElement): void {
    const advancedConfigs = modal.querySelectorAll('.advanced-config');

    advancedConfigs.forEach(config => {
      const header = config.querySelector('.advanced-config-header') as HTMLElement;

      header?.addEventListener('click', () => {
        config.classList.toggle('expanded');
      });
    });
  }

  /**
   * 初始化表单验证
   */
  private initializeFormValidation(modal: HTMLElement): void {
    const requiredFields = modal.querySelectorAll('input[required], textarea[required]');

    requiredFields.forEach(field => {
      field.addEventListener('blur', () => {
        this.validateField(field as HTMLInputElement);
      });

      field.addEventListener('input', () => {
        this.clearFieldError(field as HTMLInputElement);
      });
    });
  }

  /**
   * 验证单个字段
   */
  private validateField(field: HTMLInputElement): boolean {
    const value = field.value.trim();
    let isValid = true;
    let errorMessage = '';

    // 必填验证
    if (field.hasAttribute('required') && !value) {
      isValid = false;
      errorMessage = '此字段为必填项';
    }

    // URL格式验证
    if (field.id === 'new-url' && value) {
      try {
        new URL(value);
      } catch {
        if (!value.startsWith('/')) {
          isValid = false;
          errorMessage = '请输入有效的URL或路径';
        }
      }
    }

    // JSON格式验证
    if (['header-conditions', 'param-mapping', 'header-mapping'].includes(field.id) && value) {
      try {
        JSON.parse(value);
      } catch {
        isValid = false;
        errorMessage = '请输入有效的JSON格式';
      }
    }

    // 正则表达式验证
    if (field.id === 'url-pattern') {
      const modalElement = field.closest('.modal') as HTMLElement;
      const matchType = (modalElement?.querySelector('#url-match-type') as HTMLSelectElement)?.value;
      if (matchType === 'regex' && value) {
        try {
          new RegExp(value);
        } catch {
          isValid = false;
          errorMessage = '请输入有效的正则表达式';
        }
      }
    }

    this.setFieldValidation(field, isValid, errorMessage);
    return isValid;
  }

  /**
   * 设置字段验证状态
   */
  private setFieldValidation(field: HTMLInputElement, isValid: boolean, errorMessage: string): void {
    const formGroup = field.closest('.form-group') as HTMLElement;
    let feedback = formGroup?.querySelector('.form-feedback') as HTMLElement;

    // 移除现有状态
    field.classList.remove('is-valid', 'is-invalid');

    if (feedback) {
      feedback.remove();
    }

    if (!isValid) {
      field.classList.add('is-invalid');
      feedback = document.createElement('div');
      feedback.className = 'form-feedback invalid-feedback';
      feedback.textContent = errorMessage;
      formGroup?.appendChild(feedback);
    } else if (field.value.trim()) {
      field.classList.add('is-valid');
    }
  }

  /**
   * 清除字段错误状态
   */
  private clearFieldError(field: HTMLInputElement): void {
    field.classList.remove('is-invalid');
    const formGroup = field.closest('.form-group') as HTMLElement;
    const feedback = formGroup?.querySelector('.invalid-feedback');
    feedback?.remove();
  }

  /**
   * 测试规则
   */
  private testRule(modal: HTMLElement): void {
    const ruleData = this.extractRuleData(modal, false);
    if (!ruleData) return;

    // 创建测试界面
    const testModal = this.createTestModal(ruleData);
    document.body.appendChild(testModal);
  }

  /**
   * 创建测试模态框
   */
  private createTestModal(rule: Partial<InterceptRule>): HTMLElement {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title">测试规则: ${rule.name}</h3>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label class="form-label">测试URL</label>
            <input type="text" class="form-control" id="test-url"
              placeholder="输入要测试的URL" value="https://api.example.com/v1/users">
          </div>
          <div class="form-group">
            <label class="form-label">HTTP方法</label>
            <select class="form-control form-select" id="test-method">
              <option value="GET">GET</option>
              <option value="POST">POST</option>
              <option value="PUT">PUT</option>
              <option value="DELETE">DELETE</option>
            </select>
          </div>
          <div class="test-result" id="test-result" style="display: none;">
            <h4>测试结果</h4>
            <div class="test-output"></div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">关闭</button>
          <button class="btn btn-primary" id="run-test-btn">运行测试</button>
        </div>
      </div>
    `;

    // 绑定测试事件
    const runTestBtn = modal.querySelector('#run-test-btn') as HTMLButtonElement;
    runTestBtn?.addEventListener('click', () => {
      this.runRuleTest(modal, rule);
    });

    return modal;
  }

  /**
   * 运行规则测试
   */
  private runRuleTest(modal: HTMLElement, rule: Partial<InterceptRule>): void {
    const testUrl = (modal.querySelector('#test-url') as HTMLInputElement)?.value;
    const testMethod = (modal.querySelector('#test-method') as HTMLSelectElement)?.value;
    const resultDiv = modal.querySelector('#test-result') as HTMLElement;
    const outputDiv = modal.querySelector('.test-output') as HTMLElement;

    if (!testUrl) {
      outputDiv.innerHTML = '<div class="alert alert-error">请输入测试URL</div>';
      resultDiv.style.display = 'block';
      return;
    }

    // 模拟规则匹配测试
    const isMatched = this.testRuleMatch(rule, testUrl, testMethod);
    const transformedUrl = isMatched ? this.testUrlTransformation(rule, testUrl) : null;

    outputDiv.innerHTML = `
      <div class="test-results">
        <div class="result-item">
          <span class="result-label">匹配结果:</span>
          <span class="badge badge-${isMatched ? 'success' : 'error'}">
            ${isMatched ? '匹配' : '不匹配'}
          </span>
        </div>
        ${isMatched ? `
          <div class="result-item">
            <span class="result-label">原始URL:</span>
            <code>${testUrl}</code>
          </div>
          <div class="result-item">
            <span class="result-label">转换后URL:</span>
            <code>${transformedUrl}</code>
          </div>
          <div class="result-item">
            <span class="result-label">执行模式:</span>
            <span class="badge badge-info">并行对比</span>
          </div>
        ` : `
          <div class="result-item">
            <span class="result-label">原因:</span>
            <span class="text-muted">URL模式不匹配或HTTP方法不符合条件</span>
          </div>
        `}
      </div>
    `;

    resultDiv.style.display = 'block';
  }

  /**
   * 测试规则匹配
   */
  private testRuleMatch(rule: Partial<InterceptRule>, url: string, method: string): boolean {
    const conditions = rule.conditions;
    if (!conditions) return false;

    // URL匹配测试
    const { urlPattern, urlMatchType } = conditions;
    let urlMatched = false;

    switch (urlMatchType) {
      case 'exact':
        urlMatched = url === urlPattern;
        break;
      case 'contains':
        urlMatched = url.includes(urlPattern);
        break;
      case 'startsWith':
        urlMatched = url.startsWith(urlPattern);
        break;
      case 'endsWith':
        urlMatched = url.endsWith(urlPattern);
        break;
      case 'regex':
        try {
          urlMatched = new RegExp(urlPattern).test(url);
        } catch {
          urlMatched = false;
        }
        break;
      default:
        urlMatched = url.includes(urlPattern);
    }

    if (!urlMatched) return false;

    // HTTP方法测试
    if (conditions.methods && conditions.methods.length > 0) {
      return conditions.methods.includes(method.toUpperCase());
    }

    return true;
  }

  /**
   * 测试URL转换
   */
  private testUrlTransformation(rule: Partial<InterceptRule>, originalUrl: string): string {
    const transformation = rule.transformation;
    if (!transformation) return originalUrl;

    let newUrl = transformation.newUrl;

    // 简单的参数映射测试
    if (transformation.paramMapping) {
      try {
        const urlObj = new URL(originalUrl);
        const newUrlObj = new URL(newUrl);

        for (const [oldParam, newParam] of Object.entries(transformation.paramMapping)) {
          const value = urlObj.searchParams.get(oldParam);
          if (value) {
            newUrlObj.searchParams.set(newParam, value);
          }
        }

        if (transformation.preserveOriginalParams) {
          urlObj.searchParams.forEach((value, key) => {
            if (!transformation.paramMapping![key] && !newUrlObj.searchParams.has(key)) {
              newUrlObj.searchParams.set(key, value);
            }
          });
        }

        newUrl = newUrlObj.toString();
      } catch (error) {
        console.warn('URL transformation test failed:', error);
      }
    }

    return newUrl;
  }

  /**
   * 提取规则数据
   */
  private extractRuleData(modal: HTMLElement, validate: boolean = true): Partial<InterceptRule> | null {
    // 基础信息
    const name = (modal.querySelector('#rule-name') as HTMLInputElement)?.value.trim();
    const description = (modal.querySelector('#rule-description') as HTMLTextAreaElement)?.value.trim();
    const priority = parseInt((modal.querySelector('#rule-priority') as HTMLInputElement)?.value || '100');
    // 移除mode选择，固定使用并行对比模式
    const enabled = (modal.querySelector('#rule-enabled') as HTMLInputElement)?.checked;

    // 匹配条件
    const urlPattern = (modal.querySelector('#url-pattern') as HTMLInputElement)?.value.trim();
    const urlMatchType = (modal.querySelector('#url-match-type') as HTMLSelectElement)?.value as any;

    const methodCheckboxes = modal.querySelectorAll('input[type="checkbox"][id^="method-"]:checked');
    const methods = Array.from(methodCheckboxes).map(cb => (cb as HTMLInputElement).value);

    const headerConditionsText = (modal.querySelector('#header-conditions') as HTMLTextAreaElement)?.value.trim();
    let headers: Record<string, string> | undefined;

    if (headerConditionsText) {
      try {
        headers = JSON.parse(headerConditionsText);
      } catch (error) {
        if (validate) {
          this.showNotification('错误', '请求头条件JSON格式错误');
          return null;
        }
      }
    }

    // 转换规则
    const newUrl = (modal.querySelector('#new-url') as HTMLInputElement)?.value.trim();

    const paramMappingText = (modal.querySelector('#param-mapping') as HTMLTextAreaElement)?.value.trim();
    let paramMapping: Record<string, string> | undefined;

    if (paramMappingText) {
      try {
        paramMapping = JSON.parse(paramMappingText);
      } catch (error) {
        if (validate) {
          this.showNotification('错误', '参数映射JSON格式错误');
          return null;
        }
      }
    }

    // 移除headerMapping相关代码

    const preserveOriginalParams = (modal.querySelector('#preserve-params') as HTMLInputElement)?.checked;
    const includeCookies = (modal.querySelector('#include-cookies') as HTMLInputElement)?.checked;

    // 对比设置
    const ignoreFieldsText = (modal.querySelector('#ignore-fields') as HTMLTextAreaElement)?.value.trim();
    const ignoreFields = ignoreFieldsText ? ignoreFieldsText.split('\n').filter(line => line.trim()) : undefined;

    const caseSensitive = (modal.querySelector('#case-sensitive') as HTMLInputElement)?.checked;
    const arrayOrderSensitive = (modal.querySelector('#array-order-sensitive') as HTMLInputElement)?.checked;
    const numericTolerance = parseFloat((modal.querySelector('#numeric-tolerance') as HTMLInputElement)?.value || '0');

    // 验证必填字段
    if (validate) {
      if (!name) {
        this.showNotification('错误', '规则名称不能为空');
        return null;
      }
      if (!urlPattern) {
        this.showNotification('错误', 'URL匹配模式不能为空');
        return null;
      }
      if (!newUrl) {
        this.showNotification('错误', '新接口URL不能为空');
        return null;
      }
    }

    return {
      name,
      description: description || undefined,
      priority,
      enabled: enabled !== false,
      conditions: {
        urlPattern,
        urlMatchType,
        methods: methods.length > 0 ? methods : undefined,
        headers
      },
      transformation: {
        newUrl,
        paramMapping,
        preserveOriginalParams,
        includeCookies
      },
      diffConfig: {
        ignoreFields,
        caseSensitive,
        arrayOrderSensitive,
        numericTolerance: numericTolerance > 0 ? numericTolerance : undefined
      }
    };
  }

  /**
   * 创建新规则
   */
  private async createRule(ruleData: Partial<InterceptRule>): Promise<void> {
    const rule: InterceptRule = {
      id: this.generateId(),
      name: ruleData.name!,
      description: ruleData.description,
      enabled: ruleData.enabled !== false,
      priority: ruleData.priority || 100,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      conditions: ruleData.conditions!,
      transformation: ruleData.transformation!,
      diffConfig: ruleData.diffConfig
    };

    this.rules.push(rule);
    await this.saveRules();

    // 刷新主界面
    const mainModal = document.querySelector('.api-migration-validator-modal');
    if (mainModal) {
      this.renderRulesList(mainModal as HTMLElement);
    }

    await this.showNotification('成功', `规则 "${rule.name}" 创建成功`);
  }

  /**
   * 更新规则
   */
  private async updateRule(ruleId: string, ruleData: Partial<InterceptRule>): Promise<void> {
    const ruleIndex = this.rules.findIndex(r => r.id === ruleId);
    if (ruleIndex === -1) {
      await this.showNotification('错误', '规则不存在');
      return;
    }

    const existingRule = this.rules[ruleIndex];
    const updatedRule: InterceptRule = {
      ...existingRule,
      name: ruleData.name!,
      description: ruleData.description,
      enabled: ruleData.enabled !== false,
      priority: ruleData.priority || 100,
      updatedAt: Date.now(),
      conditions: ruleData.conditions!,
      transformation: ruleData.transformation!,
      diffConfig: ruleData.diffConfig
    };

    this.rules[ruleIndex] = updatedRule;
    await this.saveRules();

    // 刷新主界面
    const mainModal = document.querySelector('.api-migration-validator-modal');
    if (mainModal) {
      this.renderRulesList(mainModal as HTMLElement);
    }

    await this.showNotification('成功', `规则 "${updatedRule.name}" 更新成功`);
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return 'rule_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
  }

  /**
   * 导入规则
   */
  private importRules(): void {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (!file) return;

      try {
        const text = await file.text();
        const data = JSON.parse(text);

        if (Array.isArray(data)) {
          // 直接导入规则数组
          this.rules = [...this.rules, ...data];
        } else if (data.rules && Array.isArray(data.rules)) {
          // 导入完整的导出数据
          this.rules = [...this.rules, ...data.rules];
        } else {
          throw new Error('无效的文件格式');
        }

        await this.saveRules();

        // 刷新界面
        const mainModal = document.querySelector('.api-migration-validator-modal');
        if (mainModal) {
          this.renderRulesList(mainModal as HTMLElement);
        }

        await this.showNotification('成功', `成功导入 ${data.length || data.rules?.length || 0} 条规则`);
      } catch (error) {
        await this.showNotification('错误', '导入失败：' + (error as Error).message);
      }
    };
    input.click();
  }

  /**
   * 导出规则
   */
  private exportRules(): void {
    const exportData = {
      version: '1.0.0',
      exportTime: Date.now(),
      rules: this.rules,
      config: {
        toolVersion: this.version
      }
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = `api-migration-rules-${new Date().toISOString().split('T')[0]}.json`;
    a.click();

    URL.revokeObjectURL(url);
  }

  /**
   * 编辑规则
   */
  private editRule(ruleId: string): void {
    const rule = this.rules.find(r => r.id === ruleId);
    if (!rule) {
      this.showNotification('错误', '规则不存在');
      return;
    }
    this.showRuleEditor(rule);
  }

  /**
   * 切换规则状态
   */
  private toggleRule(ruleId: string): void {
    const rule = this.rules.find(r => r.id === ruleId);
    if (!rule) return;

    rule.enabled = !rule.enabled;
    rule.updatedAt = Date.now();

    this.saveRules();

    // 刷新界面
    const mainModal = document.querySelector('.api-migration-validator-modal');
    if (mainModal) {
      this.renderRulesList(mainModal as HTMLElement);
    }

    this.showNotification('成功', `规则 "${rule.name}" 已${rule.enabled ? '启用' : '禁用'}`);
  }

  /**
   * 删除规则
   */
  private deleteRule(ruleId: string): void {
    const rule = this.rules.find(r => r.id === ruleId);
    if (!rule) return;

    if (!confirm(`确定要删除规则 "${rule.name}" 吗？此操作不可撤销。`)) {
      return;
    }

    this.rules = this.rules.filter(r => r.id !== ruleId);
    this.saveRules();

    // 刷新界面
    const mainModal = document.querySelector('.api-migration-validator-modal');
    if (mainModal) {
      this.renderRulesList(mainModal as HTMLElement);
    }

    this.showNotification('成功', `规则 "${rule.name}" 已删除`);
  }

  /**
   * 查看报告
   */
  private viewReport(reportId: string): void {
    const report = this.reports.find(r => r.id === reportId);
    if (!report) {
      this.showNotification('错误', '报告不存在');
      return;
    }

    // 直接在新页面打开完整差异对比
    this.openFullDiffPage(reportId);
  }

  /**
   * 删除报告
   */
  private async deleteReport(reportId: string): Promise<void> {
    const report = this.reports.find(r => r.id === reportId);
    if (!report) return;

    if (!confirm('确定要删除此报告吗？')) {
      return;
    }

    this.reports = this.reports.filter(r => r.id !== reportId);
    await this.saveData('apiMigrationReports', this.reports);

    // 刷新界面
    const mainModal = document.querySelector('.api-migration-validator-modal');
    if (mainModal) {
      await this.renderReportsList(mainModal as HTMLElement);
    }

    this.showNotification('成功', '报告已删除');
  }




  /**
   * 查找匹配的规则
   */
  private findMatchingRule(url: string, method: string, headers: Record<string, string>): InterceptRule | null {
    // 按优先级排序
    const sortedRules = this.rules
      .filter(rule => rule.enabled)
      .sort((a, b) => b.priority - a.priority);

    for (const rule of sortedRules) {
      // 检查URL匹配
      if (rule.conditions.urlPattern) {
        const regex = new RegExp(rule.conditions.urlPattern);
        if (!regex.test(url)) continue;
      }

      // 检查方法匹配
      if (rule.conditions.methods && rule.conditions.methods.length > 0) {
        if (!rule.conditions.methods.includes(method.toUpperCase())) continue;
      }

      // 检查头部匹配
      if (rule.conditions.headers) {
        const headerMatch = Object.entries(rule.conditions.headers).every(([headerName, expectedValue]) => {
          const headerValue = headers[headerName] || headers[headerName.toLowerCase()];
          if (!headerValue) return false;

          // 支持正则表达式匹配
          if (expectedValue.startsWith('/') && expectedValue.endsWith('/')) {
            const regex = new RegExp(expectedValue.slice(1, -1));
            return regex.test(headerValue);
          }

          return headerValue === expectedValue;
        });
        if (!headerMatch) continue;
      }

      return rule;
    }

    return null;
  }

  /**
   * 并行对比
   */
  private async parallelCompare(params: any, rule: InterceptRule): Promise<void> {
    const transformedRequest = this.transformRequest(params, rule);

    try {
      // 发送到新API
      const newResponse = await this.sendRequest(transformedRequest);

      // 获取原始响应（这里需要从拦截器中获取）
      const originalResponse = params.response;

      // 生成对比报告
      const report = await this.generateDiffReport({
        url: params.url,
        method: params.method,
        headers: params.headers,
        body: params.body,
        // 使用转换后的实际URL，而不是规则中的模板URL
        newUrl: transformedRequest.url
      } as any, rule, originalResponse, newResponse);

      // 保存报告
      this.reports.push(report);
      await this.saveData('apiMigrationReports', this.reports);

      console.log('📊 并行对比完成，报告已保存');
    } catch (error) {
      console.error('❌ 并行对比失败:', error);
    }
  }

  /**
   * 发送请求
   */
  private async sendRequest(request: any): Promise<any> {
    const startTime = Date.now();

    try {
      const response = await fetch(request.url, {
        method: request.method,
        headers: request.headers,
        body: request.body
      });

      const responseTime = Date.now() - startTime;
      const body = await response.json();

      return {
        status: response.status,
        headers: Object.fromEntries(response.headers.entries()),
        body: body,
        responseTime: responseTime
      };
    } catch (error) {
      return {
        status: 0,
        headers: {},
        body: null,
        responseTime: Date.now() - startTime,
        error: (error as Error).message
      };
    }
  }

  /**
   * 转换请求 - 与background.ts中的transformUrl保持逻辑一致
   */
  private transformRequest(originalRequest: any, rule: InterceptRule): any {
    const transformed = { ...originalRequest };

    // 应用URL转换（包含参数映射处理）
    if (rule.transformation.newUrl) {
      transformed.url = this.transformUrl(originalRequest.url, rule);
    }

    // 应用头部转换
    if ((rule.transformation as any).headerMappings) {
      (rule.transformation as any).headerMappings.forEach((mapping: any) => {
        if (mapping.action === 'add') {
          transformed.headers[mapping.key] = mapping.value;
        } else if (mapping.action === 'remove') {
          delete transformed.headers[mapping.key];
        } else if (mapping.action === 'modify') {
          if (transformed.headers[mapping.key]) {
            transformed.headers[mapping.key] = mapping.value;
          }
        }
      });
    }

    // 应用请求体转换
    if ((rule.transformation as any).bodyTransformation) {
      // 这里可以添加更复杂的请求体转换逻辑
      transformed.body = (rule.transformation as any).bodyTransformation;
    }

    return transformed;
  }

  /**
   * URL转换 - 与background.ts中的transformUrl函数保持一致
   */
  private transformUrl(originalUrl: string, rule: InterceptRule): string {
    let newUrl = rule.transformation.newUrl;

    // 处理参数映射
    if (rule.transformation.paramMapping) {
      try {
        const url = new URL(originalUrl);
        const newUrlObj = new URL(newUrl);

        // 应用参数映射
        for (const [oldParam, newParam] of Object.entries(rule.transformation.paramMapping)) {
          const value = url.searchParams.get(oldParam);
          if (value !== null) {
            newUrlObj.searchParams.set(newParam, value);
            if (!rule.transformation.preserveOriginalParams) {
              url.searchParams.delete(oldParam);
            }
          }
        }

        // 保留原始参数（如果配置了）
        if (rule.transformation.preserveOriginalParams) {
          for (const [key, value] of url.searchParams.entries()) {
            if (!newUrlObj.searchParams.has(key)) {
              newUrlObj.searchParams.set(key, value);
            }
          }
        }

        newUrl = newUrlObj.toString();
      } catch (error) {
        console.warn('URL转换失败，使用原始newUrl:', error);
        // 如果URL解析失败，回退到原始newUrl
      }
    }

    return newUrl;
  }

  /**
   * 向background script发送消息（带超时机制和Service Worker唤醒）
   */
  private async sendMessageToBackground(action: string, data?: any): Promise<any> {
    try {
      const message = {
        type: 'api-migration',
        action: action,
        data: data
      };
      console.log(`📤 发送消息到background:`, message);

      // 增加15秒超时机制（给service worker更多启动时间）
      const response = await Promise.race([
        browser.runtime.sendMessage(message),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Background通信超时(15秒)')), 15000)
        )
      ]);

      console.log(`📥 收到background响应:`, response);
      return response;
    } catch (error) {
      console.error('❌ Background通信失败:', error);
      throw error;
    }
  }

  /**
   * 生成差异报告
   */
  private async generateDiffReport(
    request: any,
    rule: InterceptRule,
    oldResponse: any,
    newResponse: any
  ): Promise<DiffReport> {
    // 使用jsondiffpatch进行对比
    const delta = (window as any).jsondiffpatch?.diff(oldResponse.body, newResponse.body);

    // 计算变更数量
    const changeCount = this.countChanges(delta);

    // 确定严重程度
    const severity = this.determineSeverity(changeCount, oldResponse, newResponse);

    // 生成HTML可视化
    const html = this.generateDiffHtml(delta, oldResponse.body, newResponse.body);

    // 获取转换后的请求信息
    const transformedRequest = this.transformRequest(request, rule);

    const report: DiffReport = {
      id: this.generateId(),
      ruleId: rule.id,
      ruleName: rule.name,
      timestamp: Date.now(),
      request: {
        url: request.url,
        method: request.method,
        headers: request.headers,
        body: request.body,
        newUrl: transformedRequest.url  // 使用转换后的实际URL
      } as any,
      responses: {
        old: oldResponse,
        new: newResponse
      },
      diff: {
        hasChanges: !!delta,
        changeCount,
        severity,
        delta
      },
      visualizations: {
        html,
        summary: this.generateDiffSummary(delta)
      }
    };

    return report;
  }

  /**
   * 计算变更数量
   */
  private countChanges(delta: any): number {
    if (!delta) return 0;

    let count = 0;
    const traverse = (obj: any) => {
      for (const key in obj) {
        if (Array.isArray(obj[key])) {
          // jsondiffpatch的数组格式
          if (obj[key].length === 1) {
            count++; // 新增
          } else if (obj[key].length === 2) {
            count++; // 修改
          } else if (obj[key].length === 3 && obj[key][2] === 0) {
            count++; // 删除
          }
        } else if (typeof obj[key] === 'object') {
          traverse(obj[key]);
        }
      }
    };

    traverse(delta);
    return count;
  }

  /**
   * 确定严重程度
   */
  private determineSeverity(changeCount: number, oldResponse: any, newResponse: any): 'none' | 'minor' | 'major' | 'critical' {
    // 状态码不同
    if (oldResponse.status !== newResponse.status) {
      return 'critical';
    }

    // 根据变更数量判断
    if (changeCount === 0) return 'none';
    if (changeCount <= 3) return 'minor';
    if (changeCount <= 10) return 'major';
    return 'critical';
  }

  /**
   * 生成差异HTML
   */
  private generateDiffHtml(delta: any, oldData: any, _newData: any): string {
    if (!(window as any).jsondiffpatch) {
      return '<div class="alert alert-warning">jsondiffpatch库未加载，无法显示可视化差异</div>';
    }

    try {
      const jsondiffpatch = (window as any).jsondiffpatch;
      return jsondiffpatch.formatters.html.format(delta, oldData);
    } catch (error) {
      console.error('生成差异HTML失败:', error);
      return '<div class="alert alert-error">生成可视化差异时发生错误</div>';
    }
  }

  /**
   * 生成差异摘要
   */
  private generateDiffSummary(delta: any): string {
    if (!delta) return '无差异';

    const changes: string[] = [];
    const traverse = (obj: any, path: string = '') => {
      for (const key in obj) {
        const currentPath = path ? `${path}.${key}` : key;
        if (Array.isArray(obj[key])) {
          if (obj[key].length === 1) {
            changes.push(`新增: ${currentPath}`);
          } else if (obj[key].length === 2) {
            changes.push(`修改: ${currentPath}`);
          } else if (obj[key].length === 3 && obj[key][2] === 0) {
            changes.push(`删除: ${currentPath}`);
          }
        } else if (typeof obj[key] === 'object') {
          traverse(obj[key], currentPath);
        }
      }
    };

    traverse(delta);
    return changes.slice(0, 10).join(', ') + (changes.length > 10 ? '...' : '');
  }

  /**
   * 在新页面打开完整的差异视图
   */
  public openFullDiffPage(reportId: string): void {
    console.log('🔍 openFullDiffPage 被调用，reportId:', reportId);
    console.log('📊 当前报告列表:', this.reports);

    const report = this.reports.find(r => r.id === reportId);
    if (!report) {
      console.error('❌ 报告不存在，reportId:', reportId);
      this.showNotification('错误', '报告不存在');
      return;
    }

    try {
      // 生成完整的HTML页面
      const fullDiffHtml = this.generateFullDiffPageHtml(report);
      console.log('📄 HTML内容生成完成，长度:', fullDiffHtml.length);

      // 创建Blob URL
      const blob = new Blob([fullDiffHtml], { type: 'text/html' });
      const url = URL.createObjectURL(blob);
      console.log('🔗 Blob URL创建完成:', url);

      // 在新标签页中打开
      const newWindow = window.open(url, '_blank');
      console.log('🪟 新窗口打开结果:', newWindow);

      if (!newWindow) {
        console.error('❌ 无法打开新窗口，可能被浏览器阻止');
        this.showNotification('错误', '无法打开新窗口，请检查浏览器弹窗设置');
      }

      // 清理URL（延迟清理，确保页面已加载）
      setTimeout(() => {
        URL.revokeObjectURL(url);
        console.log('🧹 Blob URL已清理');
      }, 1000);
    } catch (error) {
      console.error('❌ 打开新页面时发生错误:', error);
      this.showNotification('错误', '打开新页面时发生错误');
    }
  }

  /**
   * 生成完整差异页面的HTML - 左右对比布局
   */
  private generateFullDiffPageHtml(report: DiffReport): string {
    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API差异对比 - ${this.extractApiName(report.request.url)}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8f9fa;
            line-height: 1.5;
            color: #333;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header h1 {
            font-size: 24px;
            margin-bottom: 8px;
        }
        .header .meta {
            opacity: 0.9;
            font-size: 14px;
        }
        .api-info-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }
        .api-info-panel {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .api-info-panel .panel-header {
            padding: 12px 20px;
            font-weight: 600;
            font-size: 14px;
            border-bottom: 2px solid #e9ecef;
        }
        .old-api .panel-header {
            background: #fff3cd;
            color: #856404;
            border-bottom-color: #ffeaa7;
        }
        .new-api .panel-header {
            background: #d4edda;
            color: #155724;
            border-bottom-color: #a3d977;
        }
        .info-content {
            padding: 16px 20px;
        }
        .info-item {
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .info-label {
            font-weight: 500;
            color: #666;
            min-width: 80px;
            font-size: 13px;
        }
        .info-value {
            color: #333;
            font-size: 13px;
            word-break: break-all;
        }
        .status-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        .diff-viewer-container {
            max-width: 1400px;
            margin: 0 auto 20px auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .diff-viewer-header {
            background: #f8f9fa;
            padding: 16px 20px;
            border-bottom: 1px solid #e9ecef;
        }
        .diff-viewer-header h3 {
            margin: 0;
            font-size: 16px;
            color: #333;
        }
        .json-diff-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2px;
            background: #dee2e6;
            border-radius: 8px;
            overflow: hidden;
            min-height: 500px;
        }
        .json-panel {
            background: white;
            display: flex;
            flex-direction: column;
        }
        .json-panel-header {
            padding: 14px 20px;
            font-weight: 600;
            font-size: 15px;
            border-bottom: 1px solid #e9ecef;
            text-align: center;
        }
        .old-json .json-panel-header {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            color: #856404;
            border-bottom-color: #ffeaa7;
        }
        .new-json .json-panel-header {
            background: linear-gradient(135deg, #d4edda 0%, #a3d977 100%);
            color: #155724;
            border-bottom-color: #a3d977;
        }
        .json-panel .json-content {
            flex: 1;
            padding: 20px;
            overflow: auto;
            background: #f8f9fa;
            max-height: 600px;
        }
        .json-panel .json-content pre {
            margin: 0;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', monospace;
            font-size: 13px;
            line-height: 1.5;
            color: #2d3748;
            white-space: pre-wrap;
            word-break: break-word;
            background: white;
            padding: 16px;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .diff-summary {
            background: #fff;
            margin: 0 20px 20px 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 20px;
            text-align: center;
        }
        .diff-summary h3 {
            margin-bottom: 16px;
            color: #333;
        }
        .diff-stats {
            display: flex;
            justify-content: center;
            gap: 24px;
            flex-wrap: wrap;
        }
        .diff-stat {
            text-align: center;
        }
        .diff-stat .number {
            display: block;
            font-size: 24px;
            font-weight: 700;
            color: #667eea;
        }
        .diff-stat .label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .severity-badge {
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        .severity-none { background: #d4edda; color: #155724; }
        .severity-minor { background: #fff3cd; color: #856404; }
        .severity-major { background: #f8d7da; color: #721c24; }
        .severity-critical { background: #f5c6cb; color: #721c24; }

        @media (max-width: 768px) {
            .api-info-comparison {
                grid-template-columns: 1fr;
                gap: 16px;
                padding: 16px;
            }
            .diff-viewer-container {
                margin: 0 16px 20px 16px;
            }
            .json-diff-comparison {
                grid-template-columns: 1fr;
                gap: 16px;
                background: transparent;
                min-height: auto;
            }
            .json-panel {
                border-radius: 8px;
                border: 1px solid #e9ecef;
                margin-bottom: 16px;
            }
            .json-panel .json-content {
                max-height: 300px;
            }
            .header h1 {
                font-size: 20px;
            }
            .diff-stats {
                gap: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>${this.extractApiName(report.request.url)} - API对比</h1>
        <div class="meta">
            规则: ${report.ruleName} | ${new Date(report.timestamp).toLocaleString()}
        </div>
    </div>

    <div class="diff-summary">
        <h3>🔍 差异概览</h3>
        <div class="diff-stats">
            <div class="diff-stat">
                <span class="number">${report.diff.changeCount}</span>
                <span class="label">变更数量</span>
            </div>
            <div class="diff-stat">
                <span class="number severity-badge severity-${report.diff.severity}">${report.diff.severity}</span>
                <span class="label">严重程度</span>
            </div>
            <div class="diff-stat">
                <span class="number">${report.responses.old.responseTime}ms</span>
                <span class="label">原接口耗时</span>
            </div>
            <div class="diff-stat">
                <span class="number">${report.responses.new.responseTime}ms</span>
                <span class="label">新接口耗时</span>
            </div>
        </div>
    </div>

    <!-- API基本信息对比 -->
    <div class="api-info-comparison">
        <div class="api-info-panel old-api">
            <div class="panel-header">📤 原接口 (${report.request.method})</div>
            <div class="info-content">
                <div class="info-item">
                    <span class="info-label">URL:</span>
                    <span class="info-value">${report.request.url}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">状态码:</span>
                    <span class="status-badge ${report.responses.old.status >= 200 && report.responses.old.status < 300 ? 'status-success' : 'status-error'}">${report.responses.old.status}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">响应时间:</span>
                    <span class="info-value">${report.responses.old.responseTime}ms</span>
                </div>
            </div>
        </div>
        <div class="api-info-panel new-api">
            <div class="panel-header">📥 新接口 (${report.request.method})</div>
            <div class="info-content">
                <div class="info-item">
                    <span class="info-label">URL:</span>
                    <span class="info-value">${report.request.newUrl || report.request.url}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">状态码:</span>
                    <span class="status-badge ${report.responses.new.status >= 200 && report.responses.new.status < 300 ? 'status-success' : 'status-error'}">${report.responses.new.status}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">响应时间:</span>
                    <span class="info-value">${report.responses.new.responseTime}ms</span>
                </div>
            </div>
        </div>
    </div>

    <!-- JSON响应数据对比 -->
    <div class="diff-viewer-container">
        <div class="diff-viewer-header">
            <h3>🔍 JSON响应数据对比</h3>
        </div>
        <div class="json-diff-comparison">
            <div class="json-panel old-json">
                <div class="json-panel-header">📤 原接口响应</div>
                <div class="json-content">
                    <pre>${JSON.stringify(report.responses.old.body, null, 2)}</pre>
                </div>
            </div>
            <div class="json-panel new-json">
                <div class="json-panel-header">📥 新接口响应</div>
                <div class="json-content">
                    <pre>${JSON.stringify(report.responses.new.body, null, 2)}</pre>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
    `;
  }

  /**
   * 显示通知
   */
  protected async showNotification(title: string, message: string): Promise<void> {
    // 使用浏览器的Notification API或者扩展的通知系统
    try {
      // 尝试使用扩展的通知系统
      if (typeof (window as any).notificationManager !== 'undefined') {
        // 根据标题判断通知类型
        const notificationManager = (window as any).notificationManager;
        if (title.includes('成功')) {
          notificationManager.success(message);
        } else if (title.includes('错误')) {
          notificationManager.error(message);
        } else if (title.includes('警告')) {
          notificationManager.warning(message);
        } else {
          notificationManager.info(message);
        }
      } else {
        // 回退到console.log
        const icon = title.includes('成功') ? '✅' : title.includes('错误') ? '❌' : title.includes('警告') ? '⚠️' : '📢';
        console.log(`${icon} ${title}: ${message}`);
      }
    } catch (error) {
      console.error('显示通知失败:', error);
      // 最终回退方案
      alert(`${title}: ${message}`);
    }
  }
}
